import { delay, http, HttpResponse, passthrough } from 'msw';

const API_HOST =
  process.env.NEXT_PUBLIC_API_BASE_URL ?? 'http://localhost:8000';

type LoginCredentials = {
  email: string;
  password: string;
};

interface LabelPlanRequest {
  label: string;
}

export const apiHandlers = [
  http.post(API_HOST + '/s/api/v1/1/auth/verify/', () => {
    return passthrough();
  }),

  http.post(API_HOST + '/s/api/v1/1/auth/forgot-password/', () => {
    return passthrough();
  }),

  http.post(API_HOST + '/s/api/v1/1/deliveroo/validate-rider/', () => {
    return passthrough();
  }),

  http.post(API_HOST + '/s/api/v1/1/auth/login/', () => {
    return passthrough();
  }),

  http.post(API_HOST + '/s/api/v1/1/auth/refresh-token/', () => {
    return passthrough();
  }),

  http.post('http://localhost:8000/next/api/v1/1/data/subscriber/', () => {
    return passthrough();
  }),

  http.post(API_HOST + '/s/api/v1/1/auth/verify/resend/', () => {
    return passthrough();
  }),

  http.post(API_HOST + '/s/api/v1/1/basket/checkout/', () => {
    return passthrough();
  }),

  http.post('http://localhost:8000/s/api/v1/1/auth/sign-up/', () => {
    return passthrough();
  }),

  http.get(API_HOST + '/next/api/v1/1/universalLink/', async () => {
    return HttpResponse.json(
      {
        universalLink: 'special-link'
      },
      { status: 200 }
    );
  }),

  http.get(API_HOST + '/next/api/v1/1/iphone-installation-code/', async () => {
    return HttpResponse.json(
      {
        code: 'xiyd8392!ssdf8377'
      },
      { status: 200 }
    );
  }),

  // REFRESH
  http.post(/\/auth\/refresh-token\/?$/, async ({ request }) => {
    const body = (await request.json()) as {
      refreshToken?: string;
      RefreshToken?: string;
    };
    const token = body.refreshToken ?? body.RefreshToken;
    if (
      token === 'signup-refresh-token' ||
      token === 'signup-access-token' ||
      token === 'mock-refresh-token'
    ) {
      return HttpResponse.json({
        tokens: {
          accessToken: 'new-mock-access-token',
          refreshToken: 'new-mock-refresh-token',
          idToken: 'new-mock-id-token',
          expiresAt: Date.now() + 60 * 60 * 1000
        }
      });
    }

    return HttpResponse.json(
      { message: 'Invalid or expired refresh token' },
      { status: 401 }
    );
  }),

  // LOGOUT
  http.post(/\/auth\/logout\/?$/, async ({ request }) => {
    const body = (await request.json()) as {
      refreshToken?: string;
      RefreshToken?: string;
    };
    const token = body.refreshToken ?? body.RefreshToken;
    // Happy path (any valid refresh token)
    if (
      token &&
      (token.startsWith('mock') || token === 'signup-access-token')
    ) {
      return HttpResponse.json({ message: 'Logged out' }, { status: 200 });
    }
    // Unhappy path: missing/invalid token
    return HttpResponse.json(
      {
        message: 'Invalid refresh token'
      },
      { status: 400 }
    );
  }),

  // SIGNUP
  http.post(/\/auth\/sign-up\/?$/, async ({ request }) => {
    const { email, password } = (await request.json()) as LoginCredentials;
    if (!email || !password) {
      return HttpResponse.json(
        { message: 'Email and password are required' },
        { status: 400 }
      );
    }
    if (email === '<EMAIL>' || email === '<EMAIL>') {
      return HttpResponse.json(
        { message: 'User already exists' },
        { status: 409 }
      );
    }
    await new Promise((res) => setTimeout(res, 30));
    return HttpResponse.json(
      {
        tokens: {
          accessToken: 'signup-access-token',
          refreshToken: 'signup-refresh-token',
          idToken: 'signup-id-token',
          expiresAt: Date.now() + 60 * 60 * 1000
        }
      },
      { status: 200 }
    );
  }),

  // SHARE ESIM
  http.post(
    API_HOST + '/next/api/v1/1/subscriptions/:id/share-esim/',
    async ({ params }) => {
      const { id } = params;

      if (!id) {
        return HttpResponse.json(
          { success: false, message: 'Subscription ID is required' },
          { status: 400 }
        );
      }

      await delay(3000);

      return HttpResponse.json({ message: 'Success' }, { status: 200 });
    }
  ),

  // PAC CODE
  http.post(API_HOST + '/next/api/v1/1/subscription/pac-code/', async () => {
    await delay(500);

    return HttpResponse.json(
      {
        success: true,
        message: 'PAC code submitted successfully'
      },
      { status: 200 }
    );
  }),

  // LABEL PLAN
  http.put(
    API_HOST + '/next/api/v1/1/subscriptions/:id/label-plan/',
    async ({ request, params }) => {
      const { id } = params;

      if (!id) {
        return HttpResponse.json(
          { success: false, message: 'Subscription ID is required' },
          { status: 400 }
        );
      }

      const body = (await request.json()) as LabelPlanRequest;

      if (!body.label) {
        return HttpResponse.json(
          { success: false, message: 'Label is required' },
          { status: 400 }
        );
      }

      await delay(3000);

      return HttpResponse.json(
        {
          success: true,
          message: 'Plan name submitted successfully',
          data: {
            label: body.label
          }
        },
        { status: 200 }
      );
    }
  ),

  // CANCEL PLAN
  http.post(
    API_HOST + '/next/api/v1/1/subscriptions/:id/cancel',
    async ({ params }) => {
      const { id } = params;

      if (!id) {
        return HttpResponse.json(
          { success: false, message: 'Subscription ID is required' },
          { status: 400 }
        );
      }

      // Simulate API delay
      await delay(3000);

      return HttpResponse.json(
        {
          success: true,
          message: 'Plan cancellation scheduled successfully'
        },
        { status: 200 }
      );
    }
  ),

  // EXTRA ROAMING DATA
  http.post(
    API_HOST + '/next/api/v1/1/subscriptions/:id/roaming-data/',
    async ({ params }) => {
      const { id } = params;

      if (!id) {
        return HttpResponse.json(
          { success: false, message: 'Subscription ID is required' },
          { status: 400 }
        );
      }

      await delay(3000);

      return HttpResponse.json(
        {
          success: true,
          message: 'Roaming data purchase succeeded'
        },
        { status: 200 }
      );
    }
  ),

  // DEBUG PURPOSES
  http.post('*', async ({ params }) => {
    console.log('MSW: Unhandled POST request', params);
    return HttpResponse.json({ message: 'Unhandled' }, { status: 500 });
  })
];
