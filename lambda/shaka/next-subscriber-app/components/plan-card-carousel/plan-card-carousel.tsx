import React, { useEffect, useState } from 'react';
import { PlanSummary, PlanSummaryClasses } from '@/components/card/plan-card';
import { ChevronDown, PlusButtonCircled } from '@/icons/icons';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { useSwipe } from '@/hooks/useSwipe';
import { SubscriptionsApiResponse } from '@/schemas/schemas';
import { determineSubscriptionAction } from '@/src/uswitch/utils/helpers';
import {
  constructRemainingDaysMessage,
  getRemainingDays
} from '@/utils/helpers';
import { subscriptionStatus } from '@/src/uswitch/utils/constants';
import { useCurrentPlan } from '@/context/current-plan-context';
import Link from 'next/link';
import { ROUTES_CONFIG } from '@/src/deliveroo/routes/route-config';
import { useSubscription } from '@/hooks/useSubscription';
import { MAX_MAIN_PLAN_QUANTITY } from '@/src/deliveroo/app/signup/_reducer/reducer';

const SWIPE_CONFIG = {
  threshold: 50,
  preventDefaultTouchmoveEvent: false,
  trackMouse: true
};

const VIEWPORT_BREAKPOINTS = {
  smallMobile: 480,
  mobile: 767,
  tablet: 1024
};

interface PlanDetailsCarouselProps {
  subscriptions: SubscriptionsApiResponse | undefined;
  classes?: PlanSummaryClasses;
  onKeepNumberClick?: () => void;
}

export function PlanDetailsCarousel({
  subscriptions,
  classes,
  onKeepNumberClick
}: PlanDetailsCarouselProps) {
  const { setCurrentPlanId } = useCurrentPlan();
  const viewportWidth = useViewportDimensions();

  const {
    currentCard,
    navigateToCard,
    navigateToNextCard,
    navigateToPreviousCard
  } = useCarouselNavigation(subscriptions || [], setCurrentPlanId);

  const swipeHandlers = useSwipe(
    {
      onSwipedLeft: navigateToNextCard,
      onSwipedRight: navigateToPreviousCard
    },
    SWIPE_CONFIG
  );

  if (!subscriptions?.length) {
    return <EmptySubscriptionsState />;
  }

  const hasMultiplePlans = subscriptions.length > 1;
  const activePlan = subscriptions[0];

  return (
    <div className="mx-auto mt-2 2xl:ml-12">
      <CarouselContainer swipeHandlers={swipeHandlers}>
        {subscriptions.map((plan, index) => (
          <PlanCard
            key={plan.id}
            plan={plan}
            index={index}
            currentCard={currentCard}
            viewportWidth={viewportWidth}
            numberOfPlans={subscriptions.length}
            classes={classes}
            onKeepNumberClick={onKeepNumberClick}
            activePlan={activePlan}
            isFirstCard={index === 0}
          />
        ))}
      </CarouselContainer>
      {hasMultiplePlans && (
        <CarouselNavigationControls
          currentCard={currentCard}
          navigateToCard={navigateToCard}
          navigateToNextCard={navigateToNextCard}
          navigateToPreviousCard={navigateToPreviousCard}
          plans={subscriptions}
        />
      )}
      <AddNewPlanButton
        hasSinglePlan={subscriptions.length === 1}
        classes={classes}
      />
    </div>
  );
}

type ViewportWidth = {
  isSmallMobile: boolean;
  isMobile: boolean;
  isTablet: boolean;
};

function calculateTranslateXValue(viewportWidth: ViewportWidth) {
  const { isSmallMobile, isMobile, isTablet } = viewportWidth;

  if (isSmallMobile) return '15vw';
  if (isMobile) return '20vw';
  if (isTablet) return '15vw';
  return '6.5vw';
}

function determineCardPosition(
  index: number,
  currentCard: number,
  numberOfPlans: number
) {
  const cardIndexDifference = index - currentCard;

  return {
    isCurrentlyShown: cardIndexDifference === 0,
    isPrevious: cardIndexDifference === -1,
    isNext: cardIndexDifference === 1,
    isLast: cardIndexDifference === numberOfPlans - 1,
    isWrappedNext: cardIndexDifference === -(numberOfPlans - 1)
  };
}

type Position = {
  isCurrentlyShown: boolean;
  isPrevious: boolean;
  isNext: boolean;
  isLast: boolean;
  isWrappedNext: boolean;
};

function generateCardTransformStyles(
  position: Position,
  translateXValue: string
) {
  const { isCurrentlyShown, isPrevious, isNext, isLast, isWrappedNext } =
    position;

  if (isCurrentlyShown) {
    return {
      transform: 'translateX(0) scale(1)',
      zIndex: 30,
      filter: 'blur(0px)',
      opacity: 1
    };
  }

  if (isNext || isWrappedNext) {
    return {
      transform: `translateX(${translateXValue}) scale(0.8)`,
      zIndex: 10,
      filter: 'blur(5px)',
      opacity: 0.7
    };
  }

  if (isPrevious || isLast) {
    return {
      transform: `translateX(-${translateXValue}) scale(0.8)`,
      zIndex: 10,
      filter: 'blur(5px)',
      opacity: 0.7
    };
  }

  return {
    transform: 'translateX(0) scale(0.9)',
    zIndex: 5,
    filter: 'blur(2px)',
    opacity: 0.3
  };
}

function useViewportDimensions() {
  const isSmallMobile = useMediaQuery(VIEWPORT_BREAKPOINTS.smallMobile);
  const isMobile = useMediaQuery(VIEWPORT_BREAKPOINTS.mobile);
  const isTablet = useMediaQuery(VIEWPORT_BREAKPOINTS.tablet);

  return { isSmallMobile, isMobile, isTablet };
}

function useCarouselNavigation(
  subscriptions: SubscriptionsApiResponse,
  setCurrentPlanId: (val: number) => void
) {
  const [currentCard, setCurrentCard] = useState(0);

  const navigateToCard = (index: number) => {
    setCurrentCard(index);
    setCurrentPlanId(subscriptions[index].id);
  };

  const navigateToNextCard = () => {
    const nextIndex = (currentCard + 1) % subscriptions.length;
    navigateToCard(nextIndex);
  };

  const navigateToPreviousCard = () => {
    const previousIndex =
      (currentCard - 1 + subscriptions.length) % subscriptions.length;
    navigateToCard(previousIndex);
  };

  // todo - find better way
  useEffect(() => {
    if (subscriptions?.length > 0) {
      setCurrentPlanId(subscriptions[0].id);
    }
  }, []);

  return {
    currentCard,
    navigateToCard,
    navigateToNextCard,
    navigateToPreviousCard
  };
}

function EmptySubscriptionsState() {
  return null;
}

interface PlanCardProps {
  plan: SubscriptionsApiResponse[number];
  index: number;
  currentCard: number;
  viewportWidth: ViewportWidth;
  numberOfPlans: number;
  activePlan: SubscriptionsApiResponse[number];
  isFirstCard: boolean;
  classes?: PlanSummaryClasses;
  onKeepNumberClick?: () => void;
}

function PlanCard({
  plan,
  index,
  currentCard,
  viewportWidth,
  numberOfPlans,
  classes,
  onKeepNumberClick,
  activePlan,
  isFirstCard
}: PlanCardProps) {
  const position = determineCardPosition(index, currentCard, numberOfPlans);
  const translateXValue = calculateTranslateXValue(viewportWidth);
  const transformStyles = generateCardTransformStyles(
    position,
    translateXValue
  );
  // console.log(plan, 'subscription plan');

  const remainingRoamingDays = getRemainingDays(
    '2025-07-31T23:59:59.000Z',
    // plan.usage.period_end,
    new Date()
  );

  const handleActionClick = () => {
    const action = determineSubscriptionAction(plan);
    if (action === 'Keep Your Number' && onKeepNumberClick) {
      onKeepNumberClick();
    }
  };

  return (
    <div
      className="absolute inset-0 w-full transition-all duration-300 ease-in-out"
      style={transformStyles}
      aria-hidden={index !== currentCard}
      tabIndex={index === currentCard ? 0 : -1}
      role="tabpanel"
      aria-labelledby={`card-${plan.id}`}
      aria-describedby={`card-${plan.id}`}
    >
      <PlanSummary className={classes?.card}>
        <PlanCardHeader
          plan={plan}
          activePlan={activePlan}
          isFirstCard={isFirstCard}
          classes={classes}
          onActionClick={handleActionClick}
        />
        <PlanCardBody
          plan={plan}
          remainingRoamingDays={remainingRoamingDays}
          classes={classes}
        />
      </PlanSummary>
    </div>
  );
}

function PlanCardHeader({
  plan,
  activePlan,
  isFirstCard,
  classes,
  onActionClick
}) {
  return (
    <PlanSummary.Header className={classes?.header}>
      {isFirstCard && <PlanSummary.MainLabel className={classes?.mainLabel} />}
      <PlanSummary.PlanType className={classes?.planType}>
        {/*check backend for the correct name !!!*/}
        {activePlan.user_subscription_name || 'Unlimited'}
        {/*{activePlan.user_subscription_name || plan.current_plan.name}*/}
      </PlanSummary.PlanType>
      <div className="grid items-end gap-2 lg:grid-cols-2">
        <PlanSummary.PhoneNumber
          className={classes?.phoneNumber}
          number={plan.current_sim.current_msisdn}
        />
        <PlanSummary.Action
          onClick={onActionClick}
          className={classes?.action}
          action={determineSubscriptionAction(plan)}
        />
      </div>
    </PlanSummary.Header>
  );
}

function PlanCardBody({ plan, remainingRoamingDays, classes }) {
  return (
    <PlanSummary.Body className={classes?.body}>
      <PlanSummary.DataUsage
        className={classes?.dataUsage}
        label="UK data used"
        amount={0}
        // amount={plan.usage.uk.data.used}
      />
      <PlanSummary.DataUsage
        className={classes?.dataUsage}
        label="EU data used"
        amount={0}
        // amount={plan.usage.europe?.data.used}
      />
      <PlanSummary.DataRemainingTime
        className={classes?.dataRemainingTime}
        remainingDaysMessage={constructRemainingDaysMessage(
          remainingRoamingDays
        )}
      />
      <PlanSummary.LowDataWarning className={classes?.lowDataWarning}>
        <strong>
          {0}GB EU data left this month
          {/*{plan.usage.europe?.data.remaining}GB EU data left this month*/}
        </strong>
      </PlanSummary.LowDataWarning>
      <PlanSummary.Texts
        className={classes?.texts}
        count={0}
        // count={plan.usage.uk.sms.remaining ?? 0}
      />
      <PlanSummary.Minutes
        className={classes?.minutes}
        count={0}
        // count={plan.usage.uk.voice.remaining ?? 0}
      />
      <PlanSummary.Status
        className={classes?.status}
        status={subscriptionStatus[plan.status]}
      />
    </PlanSummary.Body>
  );
}

function CarouselContainer({ children, swipeHandlers }) {
  return (
    <div
      className="relative mx-auto mb-2 h-auto min-h-[26rem] w-full active:cursor-grabbing"
      {...swipeHandlers}
      style={{ touchAction: 'pan-y', userSelect: 'none', cursor: 'grab' }}
    >
      {children}
    </div>
  );
}

function NavigationButton({ direction, onClick }) {
  const isNext = direction === 'next';
  const rotation = isNext ? '-rotate-90' : 'rotate-90';
  const label = isNext ? 'Next plan' : 'Previous plan';

  return (
    <button
      onClick={onClick}
      className="bg-secondary relative z-10 flex h-7 w-7 cursor-pointer items-center justify-center rounded-full"
      aria-label={label}
    >
      <ChevronDown className={`h-6 w-6 ${rotation}`} />
    </button>
  );
}

function PaginationDots({ plans, currentCard, onDotClick }) {
  return (
    <div className="bg-secondary flex space-x-3 rounded-2xl p-3">
      {plans.map((plan: SubscriptionsApiResponse[number], index: number) => (
        <button
          key={index}
          onClick={() => onDotClick(index)}
          className={`h-3 w-3 rounded-full transition-all duration-200 ${
            currentCard === index
              ? 'bg-primary scale-110'
              : 'bg-gray-300 hover:bg-gray-400'
          }`}
          aria-label={`Go to plan ${index + 1}`}
          aria-controls={`card-panel-${plan.id}`}
        />
      ))}
    </div>
  );
}

function CarouselNavigationControls({
  currentCard,
  navigateToCard,
  navigateToNextCard,
  navigateToPreviousCard,
  plans
}) {
  return (
    <div className="plan-card-width-restriction mt-[110px] flex items-center justify-between">
      <NavigationButton direction="previous" onClick={navigateToPreviousCard} />
      <PaginationDots
        plans={plans}
        currentCard={currentCard}
        onDotClick={navigateToCard}
      />
      <NavigationButton direction="next" onClick={navigateToNextCard} />
    </div>
  );
}

const PLAN_SELECTION_ROUTE = ROUTES_CONFIG['plan-selection'].path;

function PlanLimitMessage() {
  return (
    <strong className="text-text">
      You have reached the maximum number of plans
    </strong>
  );
}

interface AddPlanLinkProps {
  hasSinglePlan: boolean;
  classes?: {
    addNewPlanButton?: string;
    addNewPlanButtonIconFill?: string;
  };
}

function AddPlanLink({ hasSinglePlan, classes }: AddPlanLinkProps) {
  return (
    <Link
      href={PLAN_SELECTION_ROUTE}
      className={`plan-card-width-restriction hover:bg-secondary-hover hover:text-primary ${hasSinglePlan ? 'mt-12' : 'mt-8'} flex items-center justify-center gap-3 rounded-[2px] border-2 bg-white p-3 font-bold ${classes?.addNewPlanButton || ''}`}
    >
      <PlusButtonCircled fill={classes?.addNewPlanButtonIconFill} />
      Add new plan
    </Link>
  );
}

function AddNewPlanButton({ classes, hasSinglePlan }) {
  const { subscriptions } = useSubscription();
  const exceededPlansLimit =
    subscriptions && subscriptions.length >= MAX_MAIN_PLAN_QUANTITY;

  return (
    <div className="mt-8">
      {exceededPlansLimit ? (
        <PlanLimitMessage />
      ) : (
        <AddPlanLink hasSinglePlan={hasSinglePlan} classes={classes} />
      )}
    </div>
  );
}
