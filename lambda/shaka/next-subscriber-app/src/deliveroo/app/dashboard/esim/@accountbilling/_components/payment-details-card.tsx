import React, { useEffect, useState } from 'react';
import { PlainCard } from '@/components/plain-card/plain-card';
import Image from 'next/image';
import stripeIcon from '@/public/images/stripe-icon.png';
import Button from '@/components/button/button';
import { GenericModal } from '@/components/generic-modal/generic-modal';
import { useCheckout, PaymentElement } from '@stripe/react-stripe-js';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import { LoadingSpinner } from '@/icons/icons';
import { useSearchParams } from 'next/navigation';
import { useCreateSetupSession } from '@/hooks/useCreateSetupSession';
import { StripeCheckoutElementsOptions } from '@stripe/stripe-js';
import { StripeMode } from '@/lib/stripe/types';
import { StripeProvider } from '@/lib/stripe/stripe-provider';

const elementsOptions: StripeCheckoutElementsOptions = {
  appearance: {
    theme: 'flat',
    rules: {
      '.AccordionItem': {
        backgroundColor: 'transparent',
        borderColor: 'transparent',
        paddingLeft: '1px',
        paddingRight: '1px'
      },
      '.Label': {
        color: '#141414',
        fontWeight: '700',
        marginBottom: '8px'
      },
      '.Input': {
        marginBottom: '8px',
        borderRadius: '2px',
        color: '#141414',
        backgroundColor: '#fff',
        border: '1px solid #dddde0'
      },
      '.Input:focus': {
        boxShadow: 'none',
        outline: '1px solid  #141414'
      },
      '.Input--invalid': {
        boxShadow: 'none',
        color: '#ea0040'
      },
      '.Error': {
        color: '#ea0040',
        fontSize: '14px'
      }
    },
    variables: {
      colorPrimary: '#141414',
      // colorBackground: 'transparent',
      iconCardErrorColor: '#141414',
      iconCardCvcErrorColor: '#141414'
    }
  }
};
const paymentMode: StripeMode = 'custom';

export function UserPaymentDetailsCard() {
  const [isOpen, setIsOpen] = useState(false);

  // const searchParams = useSearchParams();
  // const cardDetailEditComplete = searchParams.get('success') || '';
  //
  // useEffect(() => {
  //   if (cardDetailEditComplete) {
  //     alert('Card details updated successfully');
  //   }
  // }, [cardDetailEditComplete]);

  const { createSetupSession, setupSession, isPending, error } =
    useCreateSetupSession();

  const handleEditPaymentClick = () => {
    createSetupSession();
    setIsOpen(true);
  };

  return (
    <PlainCard as="article" className="mb-12 rounded border-none shadow-none">
      <div className="bg-gray-subtle text-default mb-4 rounded p-3">
        <p className="text-text">Card ending **** **** **** 2199</p>
      </div>
      <div className="text-default flex flex-wrap items-center justify-between gap-2">
        <div className="flex grow items-center gap-3">
          <p className="text-text">Powered by</p>
          <Image width={74} height={31} src={stripeIcon} alt="Stripe icon" />
        </div>
        <Button onClick={handleEditPaymentClick} variant="secondary">
          Edit Payment
        </Button>
        <StripeProvider
          clientSecret={setupSession?.clientSecret}
          mode={paymentMode}
          options={{
            appearance: elementsOptions.appearance
          }}
        >
          <GenericModal isOpen={isOpen} setIsOpen={setIsOpen}>
            <PaymentModalContent error={error} isPending={isPending} />
          </GenericModal>
        </StripeProvider>
      </div>
    </PlainCard>
  );
}

interface PaymentModalContentProps {
  error?: Error | null;
  isPending: boolean;
}

function PaymentModalContent({ error, isPending }: PaymentModalContentProps) {
  const { confirm } = useCheckout();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    // differenc ebetween isPending and loading ?
    // loadign redundnat ?
    try {
      const result = await confirm();
      console.log(result, 'result');
      // if (result.type === 'error') {
      //   setError(result.error);
      // }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // check for stolen card error etc
  if (error) {
    return (
      <FormAlert
        className="mt-4"
        title="Setup Error"
        variant="error"
        messages={[`Error: ${error.message}`]}
      />
    );
  }

  if (isPending) {
    return <PaymentFormLoading />;
  }

  return (
    <PaymentForm onSubmit={handleSubmit} loading={loading} error={error} />
  );
}

interface PaymentFormProps {
  onSubmit: (e: React.FormEvent) => void;
  loading: boolean;
  error?: Error | null;
  children?: React.ReactNode;
}

function PaymentForm({ onSubmit, loading, error, children }: PaymentFormProps) {
  return (
    <form onSubmit={onSubmit}>
      <PaymentElement />
      {children}
      <div className="mt-6">{error && <div className="mb-4">:(</div>}</div>
      <Button
        className="mx-auto mb-6 block w-full"
        disabled={loading}
        isLoading={loading}
        variant="primary"
      >
        {loading ? 'Processing...' : 'Change card details'}
      </Button>
    </form>
  );
}

interface PaymentFormLoadingProps {
  message?: string;
}

function PaymentFormLoading({
  message = 'Setting up payment form...'
}: PaymentFormLoadingProps) {
  return (
    <div className="flex flex-col items-center justify-center py-8">
      <LoadingSpinner />
      <p className="mt-4">{message}</p>
    </div>
  );
}
