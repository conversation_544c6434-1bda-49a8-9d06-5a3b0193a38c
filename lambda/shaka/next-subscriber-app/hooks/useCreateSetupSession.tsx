import { useMutation } from '@tanstack/react-query';
import { paymentService } from '@/services/paymentService';
import { useAuth } from '@/auth/hooks/use-auth';
import { ROUTES_CONFIG } from '@/src/deliveroo/routes/route-config';

const returnUrl = ROUTES_CONFIG['card-detail-change-return'].path;

export function useCreateSetupSession() {
  const { apiClient } = useAuth();

  const {
    mutate: createSetupSession,
    data: setupSession,
    isPending,
    error
  } = useMutation({
    mutationFn: () => paymentService.createSetupSession(apiClient, returnUrl)
  });

  return {
    createSetupSession,
    setupSession,
    isPending,
    error
  };
}
